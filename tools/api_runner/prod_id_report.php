<?php


// const BASE_URL = 'https://api2b.versium.com/q2.php?vkey=6f1ba41224002559d0ae8539d334d6b2&';
const BASE_URL = 'https://api2b-stg.versium.com/q2.php?vkey=3b5ac175f8938aec76944acdc8314ec9&';

const BUS_HEADER_MAP = [
    'FirstName' => 'd_first',
    'LastName' => 'd_last',
    'CorpAddress' => 'd_fulladdr',
    'CorpCity' => 'd_city',
    'CorpState' => 'd_state',
    'CorpZip' => 'd_zip',
    'BizPersonEmail' => 'd_email',
    'CorpPhone' => 'd_phone',
    'CorpName' => 'd_busname',
    'CorpDomain' => 'd_domain',
    'LIProfileURL' => 'd_liurl',
];
const CONSUMER_HEADER_MAP = [
    'FirstName' => 'd_first',
    'LastName' => 'd_last',
    'Address' => 'd_fulladdr',
    'City' => 'd_city',
    'State' => 'd_state',
    'Zip' => 'd_zip',
    'EmailAddr' => 'd_email',
    'Phone' => 'd_phone',
    'CorpName' => 'd_busname',
    'CorpDomain' => 'd_domain',
    'LIProfileURL' => 'd_liurl',
];
const CUSTOM_HEADER_MAP = [
    'Phone1' => 'd_phone',
    'FirstName' => 'd_first',
    'LastName' => 'd_last',
    'Address' => 'd_fulladdr',
];
const LISTS = [
    'TS_People_Phones.csv' => [
        'headerMap' => CUSTOM_HEADER_MAP,
    ],
    'Filtered.csv' => [
        'headerMap' => CUSTOM_HEADER_MAP,
    ],
    'Custom.csv' => [
        'headerMap' => CUSTOM_HEADER_MAP,
    ],
];


function runTest(
    string $inputList,
    array $inputParams,
    array $prodIds,
)
{
    # vars
    $counter = [
        'curl_errors' => 0,
        'api2b_errors' => 0,
        'returned_P0' => 0,
        'returned_LF' => 0,
        'returned_F0' => 0,
        'returned_L0' => 0,
        'returned_AS0' => 0,
        'addr_match' => 0,
        'example_url' => '',
    ];
    $urlExample = null;
    # curl_multi stuff
    $mh = curl_multi_init();
    $handles = [];
    $batchSize = 100;

    # read in $inputList
    $fp = fopen('./lists/' . $inputList, 'r');
    $header = fgetcsv($fp);

    while (($rec = fgetcsv($fp)) !== false) {
        # gather params based on selected inputs
        $inputRec = array_combine($header, $rec);
        $params = getParams($inputRec, $inputParams, $inputList);
        $reqUrl = createReqUrl($params, $prodIds);

        if ($urlExample == null) {
            $urlExample = $reqUrl;
            $counter['example_url'] = $urlExample;
        }

        # init curl
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $reqUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_multi_add_handle($mh, $ch);
        $handles[] = [$ch, $inputRec];

        # send batch
        if (count($handles) === $batchSize) {
            // echo "sending batch \n";
            $responses = sendBatchRequest($mh, $handles, $counter);

            foreach($responses as $response) {
                processResponse($response, $counter);
            }
        }

    }
    fclose($fp);

    # send any remaining requests
    if (count($handles) > 0) {
        // echo "sending FINAL batch \n";
        $responses = sendBatchRequest($mh, $handles, $counter);

        foreach($responses as $response) {
            processResponse($response, $counter);
        }
    }


    $curlErrs = $counter['curl_errors'];
    $api2bErrs = $counter['api2b_errors'];
    $prodIdsStr = implode($prodIds);

    echo "/******************* Script Finished: ProdIds: $prodIdsStr ******************/ \n";
    echo "There were $curlErrs request errors. \n";
    echo "There were $api2bErrs api2b errors. \n";
    echo "Example Request Url: $urlExample \n";

    var_dump($counter);

    # write results to csv
    writeToCSV($counter, $prodIdsStr);
}

function getParams(array $inputRec, array $inputParams, string $list): array
{
    $params = [];
    foreach($inputRec as $columnName => $v) {
        if (isset(LISTS[$list]['headerMap'][$columnName])) {
            $queryParam = LISTS[$list]['headerMap'][$columnName];

            if (in_array($queryParam, $inputParams)) {
                $params[$queryParam] = $v;
            }
        }
    }
    return $params;
}

function createReqUrl(array $params = [], array $prodIds = null): string
{
    $auxParams = [
        'cacheBuster' => rand(),
        'cfg_maxrecs' => 1,

        'prodids' => implode($prodIds),
        // 'cfg_mc' => 'P0',
        // 'cfg_mc' => 'F0,L0',
        'cfg_mc' => 'AS0',
        'cfg_output' => 'stats2,basic'
    ];

    return BASE_URL . http_build_query(array_merge($params, $auxParams));
}

function sendBatchRequest($multiHandle, &$handles, &$counter): array
{
    $responses = [];

    # execute multi curl
    $running = null;
    do {
      curl_multi_exec($multiHandle, $running);
    } while ($running);

    # process the responses
    foreach ($handles as [$ch, $inputRec]) {
        $response = curl_multi_getcontent($ch);
        $errNum = curl_errno($ch);
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        // $requestTime = curl_getinfo($ch, CURLINFO_TOTAL_TIME);

        # store the response
        $responses[] = [json_decode($response, true, 10), $inputRec];

        # curl errors
        if ($errNum || $status != 200) {
            $counter['curl_errors'] += 1;
        }

        # api2b errors
        $jsonRes = json_decode($response);
        if (isset($jsonRes->Versium->errors)) {
            $counter['api2b_errors'] += 1;
        }

        # track query time
        // if (isset($jsonRes->Versium->{'query-time'})) {
        //     array_push($queryTimes, $jsonRes->Versium->{'query-time'});
        // }

        curl_multi_remove_handle($multiHandle, $ch);
    }
    $handles = [];

    return $responses;
}

function processResponse($response, array &$counter)
{
    $result = $response[0]['Versium']['results'][0];
    $inputRec = $response[1];
    // var_dump($response);
    // var_dump($result);

    # Parse RawMatchCodes
    if (isset($result['#RawMatchCodes'])) {
        $rawMatchCodes = $result['#RawMatchCodes'];

        if (str_contains($rawMatchCodes, 'AS0')) {
            $counter['returned_AS0'] += 1;

            if (str_contains($rawMatchCodes, 'LF')) {
                $counter['returned_LF'] += 1;
            }
        }

        // if (str_contains($rawMatchCodes, 'F0')) {
        //     $counter['returned_F0'] += 1;
        // }

        // if (str_contains($rawMatchCodes, 'L0')) {
        //     $counter['returned_L0'] += 1;

        //     // check addr accuracy
        //     if ($result['Address'] == $inputRec['Address']) {
        //         $counter['addr_match'] += 1;
        //     }
        // }

        // if (str_contains($rawMatchCodes, 'P0')) {
        //     $counter['returned_P0'] += 1;

        //     if (str_contains($rawMatchCodes, 'LF')) {
        //         $counter['returned_LF'] += 1;
        //     }
        // }
    }
}

function writeToCSV($counter, $prodids) {
    $fp = fopen('./report.csv', 'a+');
    // $record = [
    //     $prodids,
    //     $counter['returned_F0'],
    //     $counter['returned_L0'],
    //     $counter['addr_match'],
    //     $counter['example_url'],
    // ];
    $record = [
        $prodids,
        $counter['returned_AS0'],
        $counter['returned_LF'],
        // $counter['addr_match'],
        $counter['example_url'],
    ];
    // $record = [
    //     $prodids,
    //     $counter['returned_P0'],
    //     $counter['returned_LF'],
    //     $counter['example_url'],
    // ];
    fputcsv($fp, $record);
    fclose($fp);
}


# prodId/prodIds to use for each testRun
$prods = [
    ['emd5x5'],
    ['email'],
    ['emd5x5,email'],
    // ['cell4,email,durttrail,durtwp,cell,cv,basic,cell2,crawlbeewp,telcowp,wparch,auto,crawlbeetrail,whois'],
    // ['cell4,email,durttrail,durtwp,cell,cv,basic,cell2,telcowp,auto,crawlbeetrail,whois'],
    // ['cell4,email,durttrail,durtwp,cell,cv,basic,cell2,telcowp,crawlbeetrail,whois'],
    // ['cell4,email,durttrail,durtwp,cell,cv,basic,cell2,crawlbeewp,telcowp,wparch,auto,crawlbeetrail,whois,emd5x5'],
    // ['cell4,email,durttrail,durtwp,cell,cv,basic,cell2,telcowp,auto,crawlbeetrail,whois,emd5x5'],
    // ['cell4,email,durttrail,durtwp,cell,cv,basic,cell2,telcowp,crawlbeetrail,whois,emd5x5'],
];

# write report header
// $header = ['prodId', 'Returned P0', 'Returned LF', 'Example Url'];
$header = ['prodId', 'Returned AS0', 'Returned LF', 'Example Url'];
// $header = ['prodId', 'Returned F0', 'Returned L0', 'Addr Correct', 'Example Url'];
$fp = fopen('./report.csv', 'w');
fputcsv($fp, $header);
fclose($fp);

// runTest(
//     'TS_People_Phones.csv',
//     // [ 'd_first', 'd_last', 'd_fulladdr' ],
//     [ 'd_fulladdr' ],
//     ['propjp']
// );

foreach($prods as $prodIdCombo) {
    runTest(
        'TS_People_Phones.csv',
        // [ 'd_first', 'd_last', 'd_phone' ],
        [ 'd_first', 'd_last', 'd_fulladdr' ],
        $prodIdCombo
    );
}