<?php

include_once 'column_validator_helper.php';


# read in the sample data
$path = './lists/query_result_50k.csv';
$fp = fopen($path, 'r');
$header = fgetcsv($fp);

while (($rec = fgetcsv($fp)) !== false) {

    $fullRecord = array_combine($header, $rec);

    # iterate through column headers
    foreach ($header as $key) {

        # if a test exists for the column, perform it
        if (array_key_exists($key, $columnValueTests)) {
            $test = $columnValueTests[$key];
            $value = $fullRecord[$key];

            if ($test['canBeEmpty'] && empty($value)) {
                continue;
            }
            if (!preg_match($test['pattern'], $value)) {
                echo "Failed value test for $key with value $value \n";
            }
        }
    }
}

echo "Tests complete.\n";